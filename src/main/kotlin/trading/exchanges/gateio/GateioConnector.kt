@file:AIGenerated

package trading.exchanges.gateio

import io.micronaut.context.annotation.Value
import jakarta.annotation.PostConstruct
import jakarta.inject.Singleton
import kotlinx.coroutines.DelicateCoroutinesApi
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import org.slf4j.LoggerFactory
import trading.exceptions.*
import trading.exchanges.ApiResponse
import trading.exchanges.ExchangeConnector
import trading.exchanges.ExchangeConstants
import trading.exchanges.checkException
import trading.models.*
import trading.models.CanceledOrder.Companion.fromFullOrder
import trading.storage.caches.SocketRequestType
import trading.storage.repos.StrategyRepository
import trading.telegram.TelegramSender
import trading.trading.common.resolveUsdKoef
import trading.trading.holders.PriceLimit
import trading.utils.orZero
import trading.utils.scaleTo
import java.time.Instant
import java.util.concurrent.ConcurrentHashMap
import kotlin.math.absoluteValue

@Singleton
class GateioConnector(
    private val restClient: GateioRestClient,
    private val telegramSender: TelegramSender,
    private val strategyRepository: StrategyRepository,
    @Value("\${gateio.keys.secret}") private val secret: String? = null,
) : ExchangeConnector() {

    override var enabled = secret?.isNotEmpty() ?: false
    override val name: String = "gateio-usdt"

    @Volatile
    private var health = if (enabled) Health.Healthy else Health.Dead

    private val log = LoggerFactory.getLogger(this::class.java)
    private val settle = "usdt" // Using USDT as the settlement currency
    private val contractsCache = ConcurrentHashMap<String, GateioContract>()

    @PostConstruct
    @DelicateCoroutinesApi
    fun init() {
        if (!enabled) {
            return
        }

        GlobalScope.launch {
            try {
                refreshContracts()
            } catch (e: Exception) {
                log.error("Failed to initialize GateIo connector", e)
            }

            runCatching {
                // Set single mode (disable dual mode)
                managed { restClient.setSingleMode(settle) }
            }.onFailure { ex ->
                if (ex.message?.contains("NO_CHANGE") == false) {
                    log.error("Failed to enable single-mode", ex)
                }
            }

            strategyRepository.findSymbolsByExchange(name).forEach { symbol ->
                // Set cross margin mode for all positions
                runCatching {
                    managed { restClient.setCrossMode(settle, symbol) }
                }.onFailure { ex ->
                    log.error("Failed to enable cross margin mode", ex)
                }
            }
        }
    }

    private suspend fun refreshContracts() {
        val contracts = managed { restClient.getContracts(settle) }
        contracts.forEach { contract ->
            contractsCache[contract.name] = contract
        }
    }

    override fun getHealth(): ExHealth = ExHealth(name, health)

    private suspend fun <T : Any> managed(function: suspend () -> ApiResponse<T>): T {
        val response = function.invoke()
        checkException(response.exception, name, telegramSender) { health = Health.Dead }
        return handleResponse(response.body, response.code!!, response.error)
    }

    private fun <T : Any> handleResponse(
        body: T?,
        statusCode: Int,
        rawError: String?
    ): T {
        if (statusCode in 200..299) {
            if (enabled) {
                health = Health.Healthy
            }
            return body ?: throw CriticalException("Response body is null")
        }

        val errorBody = rawError ?: "unknown error"

        when (statusCode) {
            400 -> {
                // Special handling for ORDER_NOT_FOUND error
                if (errorBody.contains("ORDER_NOT_FOUND")) {
                    throw NotFoundException("Order not found: $errorBody")
                } else if (errorBody.contains("INCREASE_POSITION")) {
                    throw InsufficientCloseAmountException(errorBody)
                } else if (errorBody.contains("RISK_LIMIT_EXCEEDED")) {
                    throw RiskControlException(errorBody)
                }
                throw CriticalException("Error $statusCode: $errorBody")
            }

            401 -> throw CriticalException("Error $statusCode: $errorBody")
            403 -> {
                telegramSender.warnOnce("${name}_BLOCKED", "${name.replaceFirstChar { it.uppercase() }} api denied")
                health = Health.Dead
                throw BotBlockedException("Request forbidden")
            }

            404 -> throw NotFoundException("Not found: $errorBody")
            429 -> {
                health = Health.Overloaded
                throw TooManyRequestsException(name)
            }

            502 -> {
                health = Health.Dead
                throw BadGatewayException()
            }

            503 -> {
                health = Health.Dead
                throw OverloadedException("Overloaded: $errorBody")
            }

            else -> {
                health = Health.Dead
                throw CriticalException("Unexpected error $statusCode: $errorBody")
            }
        }
    }

    override suspend fun getPositions(symbols: List<String>): List<ExPosition> {
        return try {
            val positions = managed { restClient.getPositions(settle) }
            positions
                .map { position ->
                    val size = position.size.toDouble()
                    val symbolEx = SymbolEx(name, position.contract)

                    ExPosition(
                        symbolEx = symbolEx,
                        contracts = size,
                        enterPrice = position.entryPrice.toDouble(),
                        liquidationPrice = position.liqPrice.toDouble()
                    )
                }
        } catch (e: Exception) {
            log.error("Failed to get positions: ${e.message}")
            emptyList()
        }
    }

    override suspend fun getBalances(): List<ExBalance> {
        return try {
            val account = managed { restClient.getAccounts(settle) }
            listOf(
                ExBalance(
                    exchange = name,
                    currency = account.currency,
                    balance = account.total.toDouble()
                )
            )
        } catch (e: Exception) {
            log.error("Failed to get balances: ${e.message}")
            emptyList()
        }
    }

    override suspend fun getCandles(symbol: String, candles: Short, interval: Interval): List<ExCandle> {
        val intervalStr = when (interval) {
            Interval.ONE_MINUTE -> "1m"
            Interval.FIVE_MINUTES -> "5m"
            Interval.FIFTEEN_MINUTES -> "15m"
        }

        return try {
            val candlesticks = managed {
                restClient.getCandlesticks(
                    settle = settle,
                    contract = symbol,
                    interval = intervalStr,
                    limit = candles.toInt()
                )
            }

            candlesticks.map { candle ->
                ExCandle(
                    low = candle.l.toDouble(),
                    high = candle.h.toDouble(),
                    open = candle.o.toDouble(),
                    close = candle.c.toDouble(),
                    volume = candle.v.toDouble(),
                    timestamp = Instant.ofEpochSecond(candle.t)
                )
            }.reversed() // Reverse to get newest first
        } catch (e: Exception) {
            log.error("Failed to get candles for $symbol: ${e.message}")
            emptyList()
        }
    }

    override suspend fun getQuote(symbol: String): ExQuote {
        val tickers = managed { restClient.getTickers(settle, symbol) }
        val ticker = tickers.firstOrNull() ?: throw NotFoundException("No ticker found for $symbol")

        val symbolEx = SymbolEx(name, symbol)
        return ExQuote(
            symbolEx = symbolEx,
            ask = ticker.lowestAsk.toDouble(),
            bid = ticker.highestBid.toDouble()
        )
    }

    override suspend fun getOrders(symbol: String, opened: Boolean?, count: Short): List<FullOrder> = when (opened) {
        true -> getOrders(symbol, "open", count)
        false -> getOrders(symbol, "finished", count)
        else -> getOrders(symbol, "open", count) + getOrders(symbol, "finished", count)
    }

    private suspend fun getOrders(symbol: String, status: String, count: Short): List<FullOrder> {
        return try {
            val orders = managed {
                restClient.getOrders(
                    settle = settle,
                    status = status,
                    contract = symbol,
                    limit = count.toInt()
                )
            }

            orders.map { order ->
                val side = if (order.size > 0) Side.Buy else Side.Sell
                val filled = (order.size - order.left).toDouble()
                val clientOrderId = order.clientOrderId.removePrefix("t-") // Remove the "t-" prefix we added

                FullOrder(
                    exOrderId = order.id.toString(),
                    symbolEx = SymbolEx(name, order.contract),
                    side = side,
                    quantity = order.size.toDouble(),
                    filled = filled,
                    price = order.price.toDouble(),
                    type = OrdType.Limit,
                    status = order.toOrdStatus(),
                    botOrderId = clientOrderId
                )
            }
        } catch (e: Exception) {
            log.error("Failed to get orders for $symbol with status $status: ${e.message}")
            emptyList()
        }
    }

    override suspend fun getOrder(botOrderId: String, exOrderId: String?, symbol: String): FullOrder? {
        try {
            val orderId = exOrderId ?: run {
                // If exOrderId is not provided, we need to find the order by botOrderId
                // First, get all orders for the symbol
                val orders = getOrders(symbol, null, 100)
                // Then find the order with the matching botOrderId
                val order = orders.find { it.botOrderId == botOrderId.toGateIoBotOrderId() }
                order?.exOrderId
            } ?: return null // If we can't find the order, return null

            try {
                val order = managed { restClient.getOrder(settle, orderId) }

                val side = if (order.size > 0) Side.Buy else Side.Sell
                val filled = (order.size - order.left).toDouble()
                val clientOrderId = order.clientOrderId.removePrefix("t-") // Remove the "t-" prefix we added

                return FullOrder(
                    exOrderId = order.id.toString(),
                    symbolEx = SymbolEx(name, order.contract),
                    side = side,
                    quantity = order.size.toDouble(),
                    filled = filled,
                    price = order.price.toDouble(),
                    type = OrdType.Limit,
                    status = order.toOrdStatus(),
                    botOrderId = clientOrderId
                )
            } catch (e: NotFoundException) {
                // Order not found
                return null
            }
        } catch (e: Exception) {
            log.error("Failed to get order $exOrderId for $symbol: ${e.message}")
            return null
        }
    }

    override suspend fun getExecutions(botOrderId: String, exOrderId: String?, symbol: String): List<ExExecution> {
        try {
            val orderId = exOrderId ?: run {
                // If exOrderId is not provided, we need to find the order by botOrderId
                // First, get all orders for the symbol
                val orders = getOrders(symbol, null, 100)
                // Then find the order with the matching botOrderId
                val order = orders.find { it.botOrderId == botOrderId.toGateIoBotOrderId() }
                order?.exOrderId
            } ?: return emptyList() // If we can't find the order, return an empty list

            val trades = managed {
                restClient.getMyTrades(
                    settle = settle,
                    contract = symbol,
                    orderId = orderId
                )
            }

            return trades.map { trade ->
                val side = if (trade.size > 0) Side.Buy else Side.Sell

                ExExecution(
                    id = trade.id.toString(),
                    ordId = trade.orderId.toString(),
                    symbolEx = SymbolEx(name, trade.contract),
                    ordStatus = OrdStatus.Filled, // Trades are always filled
                    contracts = trade.size.toDouble(),
                    price = trade.price.toDouble(),
                    botOrderId = botOrderId.toGateIoBotOrderId(),
                    timeStamp = Instant.ofEpochMilli(trade.createTimeMs),
                    fee = trade.fee.toDouble().orZero(),
                    feeAsset = FeeAsset.UsdFeeAsset, // Gateio uses USDT for fees
                    side = side
                )
            }
        } catch (e: Exception) {
            log.error("Failed to get executions for order $exOrderId, symbol $symbol: ${e.message}")
            return emptyList()
        }
    }

    override suspend fun marketInContracts(
        symbol: String,
        size: Double,
        side: Side,
        botOrderId: String,
        execInst: List<ExecInst>
    ): NewOrder {
        val reduceOnly = execInst.contains(ExecInst.ReduceOnly) || execInst.contains(ExecInst.Close)

        val order = managed {
            restClient.createOrder(
                settle = settle,
                contract = symbol,
                side = side,
                size = size,
                price = null, // Market order doesn't need a price
                timeInForce = "ioc", // Market orders are IOC
                clientOrderId = botOrderId.toGateIoBotOrderId(),
                reduceOnly = reduceOnly,
            )
        }

        // For market orders, we need to check if the order is filled immediately
        val filled = (order.size - order.left).toDouble()
        val closed = order.status == "finished"

        return NewOrder(
            botOrderId = botOrderId.toGateIoBotOrderId(),
            exOrderId = order.id.toString(),
            filled = filled,
            closed = closed
        )
    }

    override suspend fun limitInContracts(
        symbol: String,
        size: Double,
        side: Side,
        price: Double,
        timeInForce: TimeInForce,
        botOrderId: String,
        execInst: List<ExecInst>
    ): NewOrder {
        val reduceOnly = execInst.contains(ExecInst.ReduceOnly) || execInst.contains(ExecInst.Close)
        val postOnly = execInst.contains(ExecInst.ParticipateDoNotInitiate)

        val tif = when {
            postOnly -> "poc"
            timeInForce == TimeInForce.ImmediateOrCancel -> "ioc"
            timeInForce == TimeInForce.FillOrKill -> "fok"
            else -> "gtc" // Default to GTC if missing
        }

        val order = managed {
            restClient.createOrder(
                settle = settle,
                contract = symbol,
                side = side,
                size = size,
                price = price,
                timeInForce = tif,
                clientOrderId = botOrderId.toGateIoBotOrderId(),
                reduceOnly = reduceOnly,
            )
        }

        return NewOrder(
            botOrderId = botOrderId.toGateIoBotOrderId(),
            exOrderId = order.id.toString()
        )
    }

    override suspend fun cancelAllOrders(symbols: List<String>) {
        try {
            if (symbols.isEmpty()) {
                // Cancel all orders for all symbols
                managed { restClient.cancelAllOrders(settle) }
            } else {
                // Cancel orders for each symbol
                for (symbol in symbols) {
                    try {
                        managed { restClient.cancelAllOrders(settle, symbol) }
                    } catch (e: Exception) {
                        log.error("Failed to cancel orders for symbol $symbol: ${e.message}")
                    }
                }
            }
        } catch (e: Exception) {
            log.error("Failed to cancel all orders: ${e.message}")
            throw e
        }
    }

    override suspend fun cancelOrder(
        botOrderId: String,
        exOrderId: String?,
        symbol: String
    ): CanceledOrder {
        val orderId = exOrderId ?: run {
            // If exOrderId is not provided, we need to find the order by botOrderId
            // First, get all orders for the symbol
            val orders = getOrders(symbol, true, 100) // Only get open orders
            // Then find the order with the matching botOrderId
            val order = orders.find { it.botOrderId == botOrderId.toGateIoBotOrderId() }
            order?.exOrderId
        } ?: throw OrderNotFoundException("Order not found for botOrderId: ${botOrderId.toGateIoBotOrderId()}")

        try {
            // Get the order details before canceling to know how much was filled
            val orderBeforeCancel = getOrder(botOrderId, orderId, symbol)
                ?: throw OrderNotFoundException("Order not found for orderId: $orderId")

            try {
                val canceledOrder = managed { restClient.cancelOrder(settle, orderId) }

                // Calculate how much was filled
                val filled = (canceledOrder.size - canceledOrder.left).toDouble()

                return CanceledOrder(filled)
            } catch (e: Exception) {
                // Check if this is an "ORDER_NOT_FOUND" error
                if (e is CriticalException && e.message?.contains("ORDER_NOT_FOUND") == true) {
                    log.info("Order $orderId not found for cancellation, returning order details from getOrder")
                    return orderBeforeCancel.fromFullOrder()
                }
                // If the order is already filled or canceled (404 Not Found), we can still return a CanceledOrder
                if (e is NotFoundException) {
                    return orderBeforeCancel.fromFullOrder()
                }
                // For other exceptions, rethrow
                throw e
            }
        } catch (e: Exception) {
            log.error("Failed to cancel order $orderId: ${e.message}")
            throw e
        }
    }

    override suspend fun updateOrder(
        symbol: String,
        botOrderId: String,
        exOrderId: String?,
        size: Double?,
        price: Double?,
        execInst: List<ExecInst>
    ): UpdOrder {
        try {
            val orderId = exOrderId ?: run {
                // If exOrderId is not provided, we need to find the order by botOrderId
                // First, get all orders for the symbol
                val orders = getOrders(symbol, true, 100) // Only get open orders
                // Then find the order with the matching botOrderId
                val order = orders.find { it.botOrderId == botOrderId.toGateIoBotOrderId() }
                order?.exOrderId
            } ?: throw OrderNotFoundException("Order not found for botOrderId: $botOrderId")

            // Check if we have anything to update
            if (size == null && price == null) {
                return UpdOrder()
            }

            val updatedOrder = managed {
                restClient.amendOrder(
                    settle = settle,
                    orderId = orderId,
                    price = price?.toString(),
                    size = size?.toInt()
                )
            }

            return UpdOrder(updatedOrder.price.toDouble())
        } catch (e: Exception) {
            log.error("Failed to update order for $symbol: ${e.message}")
            if (e is NotFoundException) {
                throw OrderNotFoundException("Order not found for update")
            }
            throw UpdateFailedException(0)
        }
    }

    override fun isUpdateSupported(): Boolean = true

    override fun contractsToUsd(symbol: String, contracts: Double): Double =
        (strategyRepository.resolveUsdKoef(SymbolEx(name, symbol)).let {
            contracts / it
        } * contractMultiplier(symbol)).scaleTo(3)

    override fun usdToContracts(symbol: String, usdSize: Double): Double =
        (strategyRepository.resolveUsdKoef(SymbolEx(name, symbol)).let {
            usdSize * it
        } / contractMultiplier(symbol)).scaleTo(0)

    override fun contractMultiplier(symbol: String) =
        getSupportedInstruments()[symbol]?.contractSize ?: error("No contract size for $symbol")

    override suspend fun getInstrument(symbol: String): ExInstrument {
        try {
            // Get the ticker for ask and bid prices
            val ticker = managed { restClient.getTickers(settle, symbol) }.firstOrNull()
                ?: throw NotFoundException("No ticker found for $symbol")

            // Try to get the contract from the cache, or refresh the cache if not found
            val contract = contractsCache[symbol] ?: run {
                log.info("Contract not found in cache for $symbol, refreshing contracts...")
                refreshContracts()
                contractsCache[symbol] ?: throw NotFoundException("Contract not found for $symbol after refresh")
            }

            val symbolEx = SymbolEx(name, symbol)
            return ExInstrument(
                symbolEx = symbolEx,
                ask = ticker.lowestAsk.toDouble(),
                bid = ticker.highestBid.toDouble(),
                rawFunding = contract.fundingRate.toDouble(),
                fundingTime = Instant.ofEpochSecond(contract.fundingNextApply)
            )
        } catch (e: Exception) {
            log.error("Failed to get instrument for $symbol: ${e.message}")
            throw e
        }
    }

    override suspend fun getSupportedInstrumentsInternal(): Map<String, ExchangeConstants> {
        // Refresh the contracts cache
        refreshContracts()

        // Create a map of ExchangeConstants for each contract
        return contractsCache.mapValues { (_, contract) ->
            ExchangeConstants(
                contract.orderPriceRound,
                1.0,
                contract.quantoMultiplier,
            )
        }
    }

    override fun isSizeIncreaseSupported(): Boolean {
        // Gateio supports increasing the size of an existing order
        return true
    }

    override suspend fun setLeverage(symbol: String, leverage: Int) {
        try {
            managed { restClient.updatePositionLeverage(settle, symbol, leverage.toString()) }
        } catch (e: Exception) {
            log.error("Failed to set leverage for $symbol: ${e.message}")
            throw e
        }
    }

    override suspend fun getRiskLimits(symbol: String): List<ExRiskLimit> {
        return try {
            val riskLimits = managed { restClient.getRiskLimitTiers(settle, symbol) }

            riskLimits
                .map { riskLimit ->
                    ExRiskLimit(
                        usdSize = riskLimit.riskLimit.toDoubleOrNull() ?: 0.0,
                        leverage = riskLimit.leverageMax.toDoubleOrNull()?.toInt() ?: 0
                    )
                }
                .distinctBy { it.leverage } // Remove duplicates with the same leverage
        } catch (e: Exception) {
            log.error("Failed to get risk limits for $symbol: ${e.message}")
            emptyList()
        }
    }

    override suspend fun getLeverage(symbol: String): Int {
        try {
            val position = managed { restClient.getPosition(settle, symbol) }

            // If leverage is 0, it means cross margin, so use crossLeverageLimit
            val leverageStr = if (position.leverage == "0") position.crossLeverageLimit else position.leverage
            return leverageStr.toInt()
        } catch (e: NotFoundException) {
            // If position not found, return default leverage (1)
            return 1
        } catch (e: Exception) {
            log.error("Failed to get leverage for $symbol: ${e.message}")
            return 1
        }
    }

    override suspend fun getPriceLimit(symbol: String): PriceLimit? {
        try {
            // Get the contract details
            val contract = managed { restClient.getContract(settle, symbol) }

            // Get the mark price
            val markPrice = contract.markPrice

            // Calculate price limits based on the mark price and order price deviate
            // For Gateio, the price limits are typically +/- 5% from the mark price
            val priceDeviate = 0.05 // 5% deviation

            return PriceLimit(
                buyLimit = markPrice * (1 - priceDeviate),
                sellLimit = markPrice * (1 + priceDeviate),
                timestamp = Instant.now()
            )
        } catch (e: Exception) {
            log.error("Failed to get price limit for $symbol: ${e.message}")
            return null
        }
    }

    override fun checkAndDecrementRateLimit(requestType: SocketRequestType) {
        // Gateio doesn't provide a specific API for checking rate limits
        // We could implement a simple rate limiting mechanism here if needed
        // For now, we'll just log the request type
        log.debug("Rate limit check for request type: {}", requestType)
    }
}

private fun String.toGateIoBotOrderId() = this.take(28)
