@file:AIGenerated

package trading.exchanges.gateio.socket

import io.micronaut.context.annotation.Factory
import io.micronaut.context.annotation.Value
import io.micronaut.scheduling.TaskScheduler
import jakarta.inject.Singleton
import trading.events.EventPublisher
import trading.exchanges.gateio.GateioSigner
import trading.models.AIGenerated
import trading.storage.repos.StrategyRepository
import trading.trading.holders.PositionsHolder

@Factory
class GateioSocketFactory {

    @Singleton
    fun gateioSocketListener(
        eventPublisher: EventPublisher,
        positionsHolder: PositionsHolder
    ): GateioSocketListener {
        return GateioSocketListener(eventPublisher, positionsHolder)
    }

    @Singleton
    fun gateioSocketSubscriber(
        @Value("\${gateio.socket.address}") socketAddress: String,
        @Value("\${gateio.keys.id}") apiKey: String?,
        gateioSocketListener: GateioSocketListener,
        gateioSigner: GateioSigner,
        taskScheduler: TaskScheduler,
        strategyRepository: StrategyRepository
    ): GateioSocketSubscriber {
        return GateioSocketSubscriber(
            socketAddress = socketAddress,
            apiKey = apiKey,
            gateioSocketListener = gateioSocketListener,
            gateioSigner = gateioSigner,
            taskScheduler = taskScheduler,
            strategyRepository = strategyRepository,
        )
    }
}
