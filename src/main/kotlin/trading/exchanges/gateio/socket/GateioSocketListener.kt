@file:AIGenerated

package trading.exchanges.gateio.socket

import kotlinx.serialization.json.Json
import okhttp3.Response
import okhttp3.WebSocket
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import trading.events.EventPublisher
import trading.events.ExLiquidationEvent
import trading.events.ExExecutionEvent
import trading.events.Integrity
import trading.exchanges.socket.PrivateSocketListener
import trading.models.*
import trading.models.FeeAsset.Companion.toFeeAsset
import trading.trading.holders.PositionsHolder
import trading.utils.orZero
import java.time.Instant
import kotlin.math.abs

class GateioSocketListener(
    private val eventPublisher: EventPublisher,
    private val positionsHolder: PositionsHolder
) : PrivateSocketListener() {

    companion object {
        private val SUBSCRIPTION_MESSAGE = "\"channel\":\"([a-zA-Z0-9_.]*)\",\"event\":\"subscribe\"".toRegex()
    }

    override val log: Logger = LoggerFactory.getLogger(GateioSocketListener::class.java)

    private val format = Json {
        isLenient = true
        ignoreUnknownKeys = true
        explicitNulls = false
    }

    private val exchange = "gateio-usdt"

    override fun onOpen(webSocket: WebSocket, response: Response) {
        super.onOpen(webSocket, response)
        log.info("Gateio WebSocket connection established successfully")
    }

    override fun onFailure(webSocket: WebSocket, t: Throwable, response: Response?) {
        if (response?.code == 503) {
            log.warn("Gateio WebSocket connection failed with 503 Service Temporarily Unavailable. Will retry with backoff.")
        } else {
            log.warn("Gateio WebSocket connection failed: ${t.message}, response code: ${response?.code}")
        }
        super.onFailure(webSocket, t, response)
    }

    override fun onClosing(webSocket: WebSocket, code: Int, reason: String) {
        log.warn("Gateio WebSocket closing: $reason (code: $code)")
        super.onClosing(webSocket, code, reason)
    }

    override fun internalOnMessage(text: String, socket: WebSocket) {
        val receivedAt = System.nanoTime()
        try {
            // First check if this is a subscription response
            if (text.contains("\"event\":\"subscribe\"")) {
                handleSubscriptionResponse(text)
                return
            }

            // Then handle other message types
            when {
                text.contains("\"channel\":\"futures.usertrades\"") -> {
                    handleUserTradeMessage(text, receivedAt)
                }
                text.contains("\"channel\":\"futures.liquidates\"") -> {
                    handleLiquidationMessage(text, receivedAt)
                }
                text.contains("\"channel\":\"futures.positions\"") -> {
                    handlePositionMessage(text, receivedAt)
                }
                text.contains("\"channel\":\"futures.pong\"") -> {
                    // Pong response, do nothing
                }
                else -> {
                    log.warn("Unhandled message: $text")
                }
            }
        } catch (e: Exception) {
            log.error("Error processing message: $text", e)
        }
    }

    private fun handleSubscriptionResponse(text: String) {
        if (text.contains("\"error\":")) {
            log.error("Subscription error: $text")
            return
        }

        try {
            val response = format.decodeFromString<GateioSocketSubscriptionResponse>(text)
            if (response.result.status == "success") {
                log.info("Successfully subscribed to ${response.channel} for ${response.payload.joinToString(", ")}")
                log.debug("Connection ID: ${response.connId}, Trace ID: ${response.traceId}")
                if (response.channel == "futures.usertrades" || response.channel == "futures.liquidates" || response.channel == "futures.positions") {
                    log.info("Authenticated successfully to ${response.channel}")
                    onLogin()
                }
            } else {
                log.warn("Subscription to ${response.channel} failed with status: ${response.result.status}")
            }
        } catch (e: Exception) {
            // Fallback to regex parsing if the JSON structure doesn't match our model
            log.warn("Failed to parse subscription response: ${e.message}, falling back to regex parsing")
            SUBSCRIPTION_MESSAGE.findAll(text).forEach {
                log.info("Subscribed to gateio:${it.groups[1]!!.value}")
                if (it.groups[1]!!.value == "futures.usertrades" || it.groups[1]!!.value == "futures.liquidates" || it.groups[1]!!.value == "futures.positions") {
                    onLogin()
                }
            }
        }
    }

    private fun handleUserTradeMessage(text: String, receivedAt: Long) {
        try {
            // Check if this is a subscription response
            if (text.contains("\"event\":\"subscribe\"")) {
                handleSubscriptionResponse(text)
                return
            }

            val tradeResponse = format.decodeFromString<GateioSocketUserTradeResponse>(text)
            val executions = tradeResponse.result.map { trade ->
                val side = if (trade.size > 0) Side.Buy else Side.Sell
                val size = abs(trade.size)

                ExExecution(
                    id = trade.id.toString(),
                    ordId = trade.orderId,
                    symbolEx = SymbolEx(exchange, trade.contract),
                    ordStatus = OrdStatus.Filled, // Trades are always filled
                    contracts = size,
                    price = trade.price.toDouble(),
                    botOrderId = trade.text?.removePrefix("t-") ?: "unknown", // The text field contains the botOrderId
                    timeStamp = Instant.ofEpochMilli(trade.createTimeMs),
                    fee = trade.fee.orZero(),
                    feeAsset = trade.contract.toFeeAsset(),
                    side = side
                )
            }

            if (executions.isNotEmpty()) {
                eventPublisher.publishBlocking(ExExecutionEvent(Integrity.New, receivedAt, executions))
            }
        } catch (e: Exception) {
            // Check if this might be a subscription response
            if (text.contains("\"event\":\"subscribe\"")) {
                handleSubscriptionResponse(text)
            } else {
                log.error("Error processing user trade message: $text", e)
            }
        }
    }

    private fun handleLiquidationMessage(text: String, receivedAt: Long) {
        try {
            // Check if this is a subscription response
            if (text.contains("\"event\":\"subscribe\"")) {
                handleSubscriptionResponse(text)
                return
            }

            val liquidationResponse = format.decodeFromString<GateioSocketLiquidateResponse>(text)
            val liquidations = liquidationResponse.result.map { liq ->
                val side = if (liq.size > 0) Side.Buy else Side.Sell

                ExLiquidation(
                    id = liq.orderId.toString(),
                    symbolEx = SymbolEx(exchange, liq.contract),
                    size = abs(liq.size),
                    side = side
                )
            }

            if (liquidations.isNotEmpty()) {
                eventPublisher.publishBlocking(ExLiquidationEvent(Integrity.New, liquidations))
                log.info("Liquidation ws message: $text")
            }
        } catch (e: Exception) {
            // Check if this might be a subscription response
            if (text.contains("\"event\":\"subscribe\"")) {
                handleSubscriptionResponse(text)
            } else {
                log.error("Error processing liquidation message: $text", e)
            }
        }
    }

    private fun handlePositionMessage(text: String, receivedAt: Long) {
        try {
            // Check if this is a subscription response
            if (text.contains("\"event\":\"subscribe\"")) {
                handleSubscriptionResponse(text)
                return
            }

            // Log the whole message with info level as requested
            log.info("Position ws message: $text")

            val positionResponse = format.decodeFromString<GateioSocketPositionResponse>(text)
            val positions = positionResponse.result.map { pos ->
                ExPosition(
                    symbolEx = SymbolEx(exchange, pos.contract),
                    contracts = pos.size,
                    enterPrice = pos.entryPrice.toDouble(),
                    liquidationPrice = pos.liqPrice.toDouble()
                )
            }

            // Store positions in PositionsHolder
            positionsHolder.store(exchange, positions)
        } catch (e: Exception) {
            // Check if this might be a subscription response
            if (text.contains("\"event\":\"subscribe\"")) {
                handleSubscriptionResponse(text)
            } else {
                log.error("Error processing position message: $text", e)
            }
        }
    }
}
