@file:AIGenerated

package trading.exchanges.gateio.socket

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import trading.models.AIGenerated

/**
 * Subscription response for Gateio WebSocket API
 */
@Serializable
data class GateioSocketSubscriptionResponse(
    val time: Long,
    @SerialName("time_ms")
    val timeMs: Long,
    val channel: String,
    val event: String,
    val payload: List<String>,
    val result: GateioSocketSubscriptionResult,
    @SerialName("conn_id")
    val connId: String? = null,
    @SerialName("trace_id")
    val traceId: String? = null
)

/**
 * Subscription result for Gateio WebSocket API
 */
@Serializable
data class GateioSocketSubscriptionResult(
    val status: String
)

/**
 * Error response for Gateio WebSocket API
 */
@Serializable
data class GateioSocketError(
    val code: Int,
    val message: String
)

/**
 * User trade notification from Gateio WebSocket API
 */
@Serializable
data class GateioSocketUserTradeResponse(
    val time: Long,
    @SerialName("time_ms")
    val timeMs: Long,
    val channel: String,
    val event: String,
    val result: List<GateioSocketUserTrade>
)

/**
 * User trade data from Gateio WebSocket API
 */
@Serializable
data class GateioSocketUserTrade(
    val id: Long,
    @SerialName("create_time")
    val createTime: Long,
    @SerialName("create_time_ms")
    val createTimeMs: Long,
    val contract: String,
    @SerialName("order_id")
    val orderId: String,
    val size: Double,
    val price: String,
    val role: String, // taker or maker
    val text: String? = null,
    val fee: Double?
)

/**
 * Private liquidation notification from Gateio WebSocket API
 */
@Serializable
data class GateioSocketLiquidateResponse(
    val time: Long,
    @SerialName("time_ms")
    val timeMs: Long,
    val channel: String,
    val event: String,
    val result: List<GateioSocketLiquidate>
)

/**
 * Private liquidation data from Gateio WebSocket API
 */
@Serializable
data class GateioSocketLiquidate(
    val time: Long,
    val contract: String,
    val leverage: String,
    val size: Double,
    val margin: String,
    @SerialName("entry_price")
    val entryPrice: String,
    @SerialName("liq_price")
    val liqPrice: String,
    @SerialName("mark_price")
    val markPrice: String,
    @SerialName("order_id")
    val orderId: Long,
    @SerialName("order_price")
    val orderPrice: String,
    @SerialName("fill_price")
    val fillPrice: String,
    val left: Double
)

/**
 * Subscription request for Gateio WebSocket API
 */
@Serializable
data class GateioSocketSubscriptionRequest(
    val time: Long,
    val channel: String,
    val event: String,
    val payload: List<String>
)

/**
 * Authentication data for Gateio WebSocket API
 */
@Serializable
data class GateioSocketAuth(
    val method: String,
    val KEY: String,
    val SIGN: String
)

/**
 * Authenticated subscription request for Gateio WebSocket API
 */
@Serializable
data class GateioSocketAuthSubscriptionRequest(
    val time: Long,
    val channel: String,
    val event: String,
    val payload: List<String>,
    val auth: GateioSocketAuth
)

/**
 * Ping request for Gateio WebSocket API
 */
@Serializable
data class GateioSocketPingRequest(
    val time: Long,
    val channel: String
)

/**
 * Position notification from Gateio WebSocket API
 */
@Serializable
data class GateioSocketPositionResponse(
    val time: Long,
    @SerialName("time_ms")
    val timeMs: Long,
    val channel: String,
    val event: String,
    val result: List<GateioSocketPosition>
)

/**
 * Position data from Gateio WebSocket API
 */
@Serializable
data class GateioSocketPosition(
    val contract: String,
    val size: Double,
    @SerialName("entry_price")
    val entryPrice: String,
    @SerialName("liq_price")
    val liqPrice: String,
    val leverage: String = "0",
    @SerialName("cross_leverage_limit")
    val crossLeverageLimit: String = "0",
    @SerialName("update_id")
    val updateId: Long? = null
)