@file:AIGenerated

package trading.exchanges.gateio.socket

import io.micronaut.scheduling.TaskScheduler
import kotlinx.serialization.json.Json
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import trading.exceptions.scheduleCatching
import trading.exchanges.gateio.GateioSigner
import trading.exchanges.socket.SocketSubscriber
import trading.models.AIGenerated
import trading.storage.repos.StrategyRepository
import java.time.Duration.ZERO
import java.time.Duration.ofSeconds

class GateioSocketSubscriber(
    private val apiKey: String?,
    private val gateioSigner: GateioSigner,
    socketAddress: String,
    gateioSocketListener: GateioSocketListener,
    taskScheduler: TaskScheduler,
    strategyRepository: StrategyRepository,
) : SocketSubscriber(socketAddress, gateioSocketListener, taskScheduler, strategyRepository) {

    override val log: Logger = LoggerFactory.getLogger(GateioSocketSubscriber::class.java)
    override val exchange = "gateio-usdt"
    override val enabled = !apiKey.isNullOrEmpty()

    private val format = Json {
        isLenient = true
        ignoreUnknownKeys = true
        explicitNulls = false
    }

    init {
        lateinit()
        taskScheduler.scheduleCatching(this, ZERO, ofSeconds(5)) {
            sendPing()
        }
    }

    override fun subscribeToAll() {
        if (subs.isEmpty()) {
            return
        }

        log.info("Subscribing to private trade, liquidation, and position notifications for $subs")

        // Subscribe to private trades, liquidations, and positions for each symbol
        subs.forEach { symbol ->
            subscribeToUserTrades(symbol)
            subscribeToLiquidations(symbol)
            subscribeToPositions(symbol)
        }
    }

    private fun subscribeToUserTrades(symbol: String) {
        val time = System.currentTimeMillis() / 1000
        // According to Gate.io WebSocket API documentation, the message to sign should be:
        // channel + event + time
        val message = "channel=futures.usertrades&event=subscribe&time=$time"
        val sign = gateioSigner.signWebsocket(message)

        log.info("Subscribing to user trades for $symbol")

        val request = GateioSocketAuthSubscriptionRequest(
            time = time,
            channel = "futures.usertrades",
            event = "subscribe",
            payload = listOf(symbol),
            auth = GateioSocketAuth(
                method = "api_key",
                KEY = apiKey!!,
                SIGN = sign
            )
        )

        socket.send(format.encodeToString(request))
    }

    private fun subscribeToLiquidations(symbol: String) {
        val time = System.currentTimeMillis() / 1000
        // According to Gate.io WebSocket API documentation, the message to sign should be:
        // channel + event + time
        val message = "channel=futures.liquidates&event=subscribe&time=$time"
        val sign = gateioSigner.signWebsocket(message)

        log.info("Subscribing to liquidations for $symbol")

        val request = GateioSocketAuthSubscriptionRequest(
            time = time,
            channel = "futures.liquidates",
            event = "subscribe",
            payload = listOf(symbol),
            auth = GateioSocketAuth(
                method = "api_key",
                KEY = apiKey!!,
                SIGN = sign
            )
        )

        socket.send(format.encodeToString(request))
    }

    private fun subscribeToPositions(symbol: String) {
        val time = System.currentTimeMillis() / 1000
        // According to Gate.io WebSocket API documentation, the message to sign should be:
        // channel + event + time
        val message = "channel=futures.positions&event=subscribe&time=$time"
        val sign = gateioSigner.signWebsocket(message)

        log.info("Subscribing to positions for $symbol")

        val request = GateioSocketAuthSubscriptionRequest(
            time = time,
            channel = "futures.positions",
            event = "subscribe",
            payload = listOf(symbol),
            auth = GateioSocketAuth(
                method = "api_key",
                KEY = apiKey!!,
                SIGN = sign
            )
        )

        socket.send(format.encodeToString(request))
    }

    override fun sayHi() {
        if (!enabled) {
            return
        }

        // Send a ping to establish connection
        sendPing()
    }

    private fun sendPing() {
        if (established()) {
            val time = System.currentTimeMillis() / 1000
            val pingRequest = GateioSocketPingRequest(
                time = time,
                channel = "futures.ping"
            )

            socket.send(format.encodeToString(pingRequest))
        }
    }
}
