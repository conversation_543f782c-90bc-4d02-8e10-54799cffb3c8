package trading.trading.holders

import jakarta.inject.Singleton
import trading.models.ExPosition
import java.util.concurrent.ConcurrentHashMap

@Singleton
open class PositionsHolder {

    companion object {
        val SUPPORTED_EXCHANGES = listOf<String>("bitget-usdt", "gateio-usdt")
    }

    // exchange -> symbol -> position
    private val cache = ConcurrentHashMap<String, MutableMap<String, ExPosition>>()

    fun store(exchange: String, values: List<ExPosition>) {
        cache.computeIfAbsent(exchange) { ConcurrentHashMap() }
            .also { it.clear(); it } // clear the map before saving so that we have the actual list of positions
            .putAll(values.associateBy { it.symbolEx.symbol })
    }

    fun get(exchange: String): List<ExPosition> = cache[exchange]?.values?.toList() ?: emptyList()
}
