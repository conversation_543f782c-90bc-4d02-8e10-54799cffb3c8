package trading.exchanges.gateio.socket

import kotlinx.serialization.json.Json
import org.junit.jupiter.api.Test
import trading.events.EventPublisher
import io.mockk.mockk

class GateioSocketPositionTest {

    private val eventPublisher = mockk<EventPublisher>(relaxed = true)
    private val listener = GateioSocketListener(eventPublisher)

    private val format = Json {
        isLenient = true
        ignoreUnknownKeys = true
        explicitNulls = false
    }

    @Test
    fun `should parse position message correctly`() {
        val positionMessage = """
            {
                "time": 1609459200,
                "time_ms": 1609459200123,
                "channel": "futures.positions",
                "event": "update",
                "result": [
                    {
                        "contract": "BTC_USDT",
                        "size": 10.5,
                        "entry_price": "50000.0",
                        "liq_price": "45000.0",
                        "leverage": "10",
                        "cross_leverage_limit": "25",
                        "update_id": 123456
                    }
                ]
            }
        """.trimIndent()

        // Test that the message can be parsed
        val response = format.decodeFromString<GateioSocketPositionResponse>(positionMessage)
        
        assert(response.channel == "futures.positions")
        assert(response.event == "update")
        assert(response.result.size == 1)
        
        val position = response.result[0]
        assert(position.contract == "BTC_USDT")
        assert(position.size == 10.5)
        assert(position.entryPrice == "50000.0")
        assert(position.liqPrice == "45000.0")
        assert(position.leverage == "10")
        assert(position.updateId == 123456L)
    }

    @Test
    fun `should parse position response correctly`() {
        val positionMessage = """
            {
                "time": 1609459200,
                "time_ms": 1609459200123,
                "channel": "futures.positions",
                "event": "update",
                "result": [
                    {
                        "contract": "BTC_USDT",
                        "size": 10.5,
                        "entry_price": "50000.0",
                        "liq_price": "45000.0",
                        "leverage": "10"
                    },
                    {
                        "contract": "ETH_USDT",
                        "size": -5.2,
                        "entry_price": "3000.0",
                        "liq_price": "2800.0",
                        "leverage": "5"
                    }
                ]
            }
        """.trimIndent()

        // Test that the message can be parsed
        val response = format.decodeFromString<GateioSocketPositionResponse>(positionMessage)
        
        assert(response.channel == "futures.positions")
        assert(response.event == "update")
        assert(response.result.size == 2)
        
        val btcPosition = response.result[0]
        assert(btcPosition.contract == "BTC_USDT")
        assert(btcPosition.size == 10.5)
        assert(btcPosition.entryPrice == "50000.0")
        assert(btcPosition.liqPrice == "45000.0")
        
        val ethPosition = response.result[1]
        assert(ethPosition.contract == "ETH_USDT")
        assert(ethPosition.size == -5.2)
        assert(ethPosition.entryPrice == "3000.0")
        assert(ethPosition.liqPrice == "2800.0")
    }

    @Test
    fun `should parse position data correctly`() {
        val positionData = GateioSocketPosition(
            contract = "BTC_USDT",
            size = 10.5,
            entryPrice = "50000.0",
            liqPrice = "45000.0",
            leverage = "10"
        )

        // Test that position data is parsed correctly
        assert(positionData.contract == "BTC_USDT")
        assert(positionData.size == 10.5)
        assert(positionData.entryPrice == "50000.0")
        assert(positionData.liqPrice == "45000.0")
        assert(positionData.leverage == "10")
    }
}
