package trading.exchanges.gateio.socket

import com.fasterxml.jackson.databind.ObjectMapper
import org.junit.jupiter.api.Disabled
import jakarta.inject.Inject
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import org.junit.jupiter.api.Test
import org.slf4j.LoggerFactory
import trading.events.EventPublisher
import trading.exchanges.gateio.GateioSigner
import trading.exchanges.gateio.socket.GateioSocketListener
import trading.exchanges.gateio.socket.GateioSocketPingRequest
import trading.exchanges.gateio.socket.GateioSocketSubscriptionRequest
import trading.trading.holders.PositionsHolder
import io.mockk.mockk

@Disabled("Micronaut test framework not available")
class GateioSocketTest {

    private val log = LoggerFactory.getLogger(this::class.java)

    @Inject
    lateinit var eventPublisher: EventPublisher

    @Test
    fun testSocketModels() {
        val format = Json {
            isLenient = true
            ignoreUnknownKeys = true
            explicitNulls = false
        }

        // Test ping request
        val pingRequest = GateioSocketPingRequest(
            time = 1234567890,
            channel = "futures.ping"
        )
        val pingJson = format.encodeToString(pingRequest)
        log.info("Ping request JSON: $pingJson")

        // Test subscription request
        val subscriptionRequest = GateioSocketSubscriptionRequest(
            time = 1234567890,
            channel = "futures.trades",
            event = "subscribe",
            payload = listOf("BTC_USDT")
        )
        val subscriptionJson = format.encodeToString(subscriptionRequest)
        log.info("Subscription request JSON: $subscriptionJson")

        // Test listener
        val positionsHolder = mockk<PositionsHolder>(relaxed = true)
        val listener = GateioSocketListener(eventPublisher, positionsHolder)
        log.info("Created listener: $listener")
    }
}
